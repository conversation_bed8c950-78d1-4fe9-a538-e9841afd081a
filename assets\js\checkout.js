// Wait for Stripe to be available
const waitForStripe = () => {
  return new Promise((resolve) => {
    if (typeof Stripe !== 'undefined') {
      resolve();
    } else {
      const checkStripe = setInterval(() => {
        if (typeof Stripe !== 'undefined') {
          clearInterval(checkStripe);
          resolve();
        }
      }, 100);
    }
  });
};

let stripeInstance;

// Initialize after Stripe is available
waitForStripe().then(() => {
  stripeInstance = Stripe("pk_live_51QK3VGGM1wn55LV9xJGhZu1cvf3FSveaoJT4OJRXQwNVwcsLKE3Fwj7yTKiWamZmWHNg7EJtoEayQhZ6cM0GWSjg00Rb26GCuL");
  //stripeInstance = Stripe("pk_test_51QK3VGGM1wn55LV96H6XaTsZ1G0kIW9851L8vZbaJkdSWCBjKReFEYF6fWqsfoLZe8GI1pzREcjp1kS8EBP2StPJ00oPV1sXZC");

  // Create a Checkout Session with 1 second delay
  setTimeout(() => {
    initialize();
  }, 1000);
});

// Create a Checkout Session
async function initialize() {
  const fetchClientSecret = async () => {
    const response = await fetch("/create-checkout-session", {
      method: "POST",
    });
    const { clientSecret, publishableKey } = await response.json();
    stripeInstance = Stripe(publishableKey);  // Update stripe instance with the server-provided key
    return clientSecret;
  };

  const checkout = await stripeInstance.initEmbeddedCheckout({
    fetchClientSecret,
  });

  // Mount Checkout
  checkout.mount('#checkout');
}